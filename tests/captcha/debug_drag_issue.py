"""调试拖拽卡住问题的专用脚本。

专门用于诊断和修复拖拽操作中的问题。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.common.utils import logging_utils
from src.captcha.recognizer.t_sec.tencent_slider_solver import TencentSliderSolver


async def debug_drag_issue():
    """调试拖拽卡住问题。"""
    print("🔍 调试拖拽卡住问题")
    print("=" * 50)
    print("这个脚本将:")
    print("1. 导航到验证码页面")
    print("2. 触发验证码")
    print("3. 获取图片并保存")
    print("4. 执行拖拽操作（带详细日志）")
    print("5. 分析卡住的原因")
    print("=" * 50)
    
    solver = TencentSliderSolver(
        dddocr_base_url="http://10.168.1.201:7777",
        headless=False,
        browser_type="chromium"
    )
    
    async with solver:
        try:
            # 步骤1: 导航到页面
            print("\n📍 步骤1: 导航到腾讯云验证码页面...")
            await solver.navigate_to_page("https://cloud.tencent.com/product/captcha")
            await asyncio.sleep(3)
            
            # 步骤2: 点击触发按钮
            print("👆 步骤2: 点击体验按钮...")
            try:
                await solver.page.wait_for_selector('#captcha_click', timeout=10000)
                experience_button = await solver.page.query_selector('#captcha_click')
                if experience_button:
                    await experience_button.click()
                    print("✅ 按钮点击成功")
                else:
                    print("❌ 未找到体验按钮")
                    return False
            except Exception as click_error:
                print(f"❌ 点击按钮失败: {click_error}")
                return False
            
            # 步骤3: 等待验证码加载
            print("\n⏳ 步骤3: 等待验证码加载...")
            captcha_loaded = await solver.wait_for_captcha()
            if not captcha_loaded:
                print("❌ 验证码加载失败")
                return False
            print("✅ 验证码加载成功")
            
            # 步骤4: 获取图片
            print("\n🖼️  步骤4: 获取验证码图片...")
            bg_data, slider_data = await solver.get_captcha_images()
            
            if not bg_data:
                print("❌ 图片获取失败")
                return False
            
            print(f"✅ 图片获取成功: bg={len(bg_data)} bytes, slider={len(slider_data) if slider_data else 0} bytes")
            
            # 保存图片（强制PNG格式）
            try:
                from PIL import Image
                import io
                
                bg_img = Image.open(io.BytesIO(bg_data))
                bg_img.save("debug_bg.png", format='PNG')
                print("💾 背景图保存: debug_bg.png")
                
                if slider_data:
                    slider_img = Image.open(io.BytesIO(slider_data))
                    slider_img.save("debug_slider.png", format='PNG')
                    print("💾 滑块图保存: debug_slider.png")
                
            except Exception as save_error:
                print(f"⚠️  图片保存失败: {save_error}")
            
            # 步骤5: dddocr识别
            print("\n🧩 步骤5: dddocr识别...")
            try:
                from src.captcha.recognizer.t_sec.dddocr_client import DDDOCRClient
                
                client = DDDOCRClient("http://10.168.1.201:7777")
                if slider_data:
                    target_x = client.capcode(slider_data, bg_data)
                else:
                    target_x = client.capcode(bg_data, bg_data)
                
                print(f"✅ dddocr识别结果: x={target_x}")
                
                if target_x <= 0 or target_x >= 400:
                    print(f"⚠️  识别结果可能不准确，但继续测试拖拽: x={target_x}")
                    target_x = 100  # 使用一个安全的测试值
                    print(f"🔧 使用测试值进行拖拽: x={target_x}")
                
            except Exception as api_error:
                print(f"❌ dddocr识别失败: {api_error}")
                print("🔧 使用测试值进行拖拽: x=100")
                target_x = 100
            
            # 步骤6: 执行拖拽（重点调试）
            print(f"\n🖱️  步骤6: 执行拖拽操作 (x={target_x})...")
            print("注意观察控制台日志，查看拖拽过程中的详细信息")
            print("如果在这里卡住，请检查:")
            print("1. 是否有意外的键盘操作")
            print("2. 是否有焦点问题")
            print("3. 是否有iframe坐标问题")
            print("4. 是否有鼠标事件问题")
            print()
            print("开始拖拽...")
            
            try:
                drag_success = await solver._drag_slider(target_x)
                
                if drag_success:
                    print("✅ 拖拽操作完成")
                    
                    # 等待结果
                    print("\n⏳ 步骤7: 等待验证结果...")
                    await asyncio.sleep(3)
                    
                    # 检查结果
                    try:
                        success_element = await solver.page.query_selector('.tc-success')
                        error_element = await solver.page.query_selector('.tc-error')
                        
                        if success_element:
                            print("🎉 验证码解决成功！")
                        elif error_element:
                            print("❌ 验证码解决失败（但拖拽正常）")
                        else:
                            print("⚠️  验证结果未知（但拖拽正常）")
                            
                    except Exception as result_error:
                        print(f"⚠️  检查验证结果时出错: {result_error}")
                    
                    # 成功截图（使用主页面）
                    if hasattr(solver, 'main_page') and solver.main_page:
                        await solver.main_page.screenshot(path="debug_after_drag.png")
                    else:
                        await solver.page.screenshot(path="debug_after_drag.png")
                    print("📸 拖拽后截图: debug_after_drag.png")
                    
                    return True
                    
                else:
                    print("❌ 拖拽操作失败")
                    
                    # 失败截图（使用主页面）
                    if hasattr(solver, 'main_page') and solver.main_page:
                        await solver.main_page.screenshot(path="debug_drag_failed.png")
                    else:
                        await solver.page.screenshot(path="debug_drag_failed.png")
                    print("📸 拖拽失败截图: debug_drag_failed.png")
                    
                    return False
                    
            except Exception as drag_error:
                print(f"💥 拖拽操作出错: {drag_error}")
                
                # 错误截图（使用主页面）
                try:
                    if hasattr(solver, 'main_page') and solver.main_page:
                        await solver.main_page.screenshot(path="debug_drag_error.png")
                    else:
                        await solver.page.screenshot(path="debug_drag_error.png")
                    print("📸 拖拽错误截图: debug_drag_error.png")
                except:
                    pass
                
                return False
                
        except Exception as e:
            print(f"💥 调试过程中出现错误: {e}")
            
            # 错误截图（使用主页面）
            try:
                if hasattr(solver, 'main_page') and solver.main_page:
                    await solver.main_page.screenshot(path="debug_general_error.png")
                else:
                    await solver.page.screenshot(path="debug_general_error.png")
                print("📸 通用错误截图: debug_general_error.png")
            except:
                pass
            
            return False


async def main():
    """主函数。"""
    print("拖拽问题调试工具")
    print("=" * 50)
    
    try:
        print("这个工具将帮助诊断拖拽卡住的问题:")
        print("1. 详细的步骤日志")
        print("2. 图片保存和检查")
        print("3. 拖拽过程的详细监控")
        print("4. 错误截图和调试信息")
        print()
        print("请确保:")
        print("1. dddocr 服务正在运行")
        print("2. 网络连接正常")
        print("3. 浏览器可以正常访问腾讯云")
        print()
        
        input("按回车键开始调试...")
        
        success = await debug_drag_issue()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 调试结果: 拖拽正常！")
            print("生成的文件:")
            print("- debug_bg.png (背景图)")
            print("- debug_slider.png (滑块图)")
            print("- debug_after_drag.png (拖拽后截图)")
        else:
            print("❌ 调试结果: 发现问题")
            print("请检查生成的文件:")
            print("- debug_bg.png (背景图)")
            print("- debug_slider.png (滑块图)")
            print("- debug_drag_failed.png (拖拽失败截图)")
            print("- debug_drag_error.png (拖拽错误截图)")
            print("- debug_general_error.png (通用错误截图)")
            print()
            print("请查看控制台日志中的详细信息，")
            print("特别是拖拽操作开始后的日志。")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断调试")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
    
    print("\n调试结束")


if __name__ == "__main__":
    asyncio.run(main())
