"""腾讯滑块验证码测试脚本。

测试腾讯滑块验证码的识别和解决功能。
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.common.utils import logging_utils
from src.captcha.recognizer.t_sec.tencent_slider_solver import TencentSliderSolver

logger = logging.getLogger(__name__)


async def test_tencent_cloud_captcha():
    """测试腾讯云验证码产品页面的"立即体验"功能。"""
    print("=== 腾讯云验证码产品页测试 ===")
    
    solver = TencentSliderSolver(
        dddocr_base_url="http://************:7777",
        headless=False,  # 显示浏览器以便观察
        browser_type="chromium"
    )
    
    async with solver:
        try:
            # 导航到腾讯云验证码产品页
            url = "https://cloud.tencent.com/product/captcha"
            print(f"导航到: {url}")
            await solver.navigate_to_page(url)
            
            # 等待页面完全加载
            await asyncio.sleep(5)
            
            # 查找"立即体验"按钮
            experience_button = await solver.page.query_selector('#captcha_click')
            if not experience_button:
                # 尝试其他可能的选择器
                alternative_selectors = [
                    'a.tpm-btn',
                    '.tpm-btn',
                    'a[id*="captcha"]',
                    'button:has-text("立即体验")',
                    'a:has-text("立即体验")',
                    '.btn:has-text("体验")'
                ]
                
                for selector in alternative_selectors:
                    try:
                        experience_button = await solver.page.query_selector(selector)
                        if experience_button:
                            print(f"找到体验按钮: {selector}")
                            break
                    except:
                        continue
            else:
                print("找到体验按钮: #captcha_click")
            
            if not experience_button:
                print("❌ 未找到'立即体验'按钮")
                return False
            
            # 点击"立即体验"按钮
            print("点击'立即体验'按钮...")
            await experience_button.click()
            
            # 等待验证码出现
            print("等待验证码出现...")
            captcha_appeared = await solver.wait_for_captcha(timeout=15000)
            
            if not captcha_appeared:
                print("❌ 验证码未出现，可能需要额外操作")
                
                # 尝试查找其他可能触发验证码的元素
                print("尝试查找其他触发元素...")
                trigger_elements = await solver.page.query_selector_all('button, input[type="submit"], .btn')
                
                for i, element in enumerate(trigger_elements[:5]):  # 只尝试前5个元素
                    try:
                        text = await element.inner_text()
                        if any(keyword in text.lower() for keyword in ['验证', '体验', '测试', '开始']):
                            print(f"尝试点击: {text}")
                            await element.click()
                            await asyncio.sleep(2)
                            
                            # 再次检查验证码是否出现
                            if await solver.wait_for_captcha(timeout=5000):
                                captcha_appeared = True
                                break
                    except:
                        continue
            
            if captcha_appeared:
                print("✅ 检测到腾讯滑块验证码")
                
                # 解决验证码
                print("开始解决验证码...")
                success = await solver.solve_captcha(max_retries=5)
                
                if success:
                    print("🎉 验证码解决成功！")
                    
                    # 等待验证结果并截图
                    await asyncio.sleep(3)
                    
                    # 截图保存结果
                    try:
                        screenshot_path = "tencent_captcha_success.png"
                        await solver.page.screenshot(path=screenshot_path)
                        print(f"成功截图已保存: {screenshot_path}")
                    except:
                        pass
                    
                    return True
                else:
                    print("❌ 验证码解决失败")
                    
                    # 截图保存失败状态
                    try:
                        screenshot_path = "tencent_captcha_failed.png"
                        await solver.page.screenshot(path=screenshot_path)
                        print(f"失败截图已保存: {screenshot_path}")
                    except:
                        pass
                    
                    return False
            else:
                print("❌ 验证码未出现")
                return False
                
        except Exception as e:
            print(f"测试出错: {e}")
            logging_utils.logger_print(
                msg="error in tencent cloud captcha test",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return False


async def test_custom_page():
    """测试自定义页面的腾讯滑块验证码。"""
    print("\n=== 自定义页面测试 ===")
    
    # 用户可以在这里输入包含腾讯滑块验证码的页面URL
    custom_url = input("请输入包含腾讯滑块验证码的页面URL（回车跳过）: ").strip()
    
    if not custom_url:
        print("跳过自定义页面测试")
        return
    
    solver = TencentSliderSolver(
        dddocr_base_url="http://************:7777",
        headless=False,
        browser_type="chromium"
    )
    
    async with solver:
        try:
            print(f"导航到: {custom_url}")
            await solver.navigate_to_page(custom_url)
            
            # 等待用户手动触发验证码
            print("请在浏览器中手动触发验证码，然后按回车继续...")
            input()
            
            # 解决验证码
            success = await solver.solve_captcha(max_retries=5)
            
            if success:
                print("✅ 验证码解决成功！")
            else:
                print("❌ 验证码解决失败")
            
            # 等待观察结果
            print("等待10秒以便观察结果...")
            await asyncio.sleep(10)
            
        except Exception as e:
            print(f"测试出错: {e}")
            logging_utils.logger_print(
                msg="error in custom page test",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )


async def test_image_extraction():
    """测试图片提取功能。"""
    print("\n=== 图片提取测试 ===")
    
    # 创建一个简单的HTML页面来测试图片提取
    html_content = '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>腾讯滑块验证码测试</title>
    </head>
    <body>
        <div class="tc-opera">
            <div class="tc-bg-img" style="background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='); width: 300px; height: 150px;"></div>
            <div class="tc-slider-normal" style="left: 0px; top: 100px; width: 50px; height: 30px;"></div>
            <div class="tc-fg-item" style="background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='); width: 50px; height: 50px; cursor: pointer;"></div>
        </div>
    </body>
    </html>
    '''
    
    solver = TencentSliderSolver(headless=False)
    
    async with solver:
        try:
            # 加载测试HTML
            await solver.page.set_content(html_content)
            
            # 测试图片提取
            bg_data, slider_data = await solver.get_captcha_images()
            
            print(f"背景图提取: {'成功' if bg_data else '失败'}")
            print(f"滑块图提取: {'成功' if slider_data else '失败'}")
            
            if bg_data:
                print(f"背景图大小: {len(bg_data)} 字节")
            
            if slider_data:
                print(f"滑块图大小: {len(slider_data)} 字节")
            
        except Exception as e:
            print(f"图片提取测试出错: {e}")


async def main():
    """主函数。"""
    print("腾讯滑块验证码测试工具")
    print("=" * 50)
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    try:
        print("选择测试模式:")
        print("1. 腾讯云验证码产品页测试（推荐）")
        print("2. 自定义页面测试")
        print("3. 图片提取测试")
        print("4. 全部测试")
        
        choice = input("请选择 (1-4): ").strip()
        
        if choice == "1":
            await test_tencent_cloud_captcha()
        elif choice == "2":
            await test_custom_page()
        elif choice == "3":
            await test_image_extraction()
        elif choice == "4":
            await test_image_extraction()
            await test_tencent_cloud_captcha()
            await test_custom_page()
        else:
            print("无效选择")
    
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n测试出错: {e}")
        logging_utils.logger_print(
            msg="test failed",
            custom_logger=logger,
            use_exception=True,
            exception=e
        )
    
    print("\n测试结束")


if __name__ == "__main__":
    asyncio.run(main())
