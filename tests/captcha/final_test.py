"""最终测试：验证所有修复是否正常工作。

这是一个完整的端到端测试，验证腾讯滑块验证码的完整解决流程。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.common.utils import logging_utils
from src.captcha.recognizer.t_sec.tencent_slider_solver import TencentSliderSolver


async def final_test():
    """最终完整测试。"""
    print("🎯 腾讯滑块验证码最终测试")
    print("=" * 50)
    print("测试内容:")
    print("✅ iframe 检测和切换")
    print("✅ 图片提取和 base64 编码")
    print("✅ /capcode API 调用")
    print("✅ 坐标转换和拖拽")
    print("✅ 验证结果检查")
    print("=" * 50)
    
    solver = TencentSliderSolver(
        dddocr_base_url="http://************:7777",
        headless=False,
        browser_type="chromium"
    )
    
    async with solver:
        try:
            print("\n🚀 开始完整的验证码解决流程...")
            
            # 导航到页面
            print("📍 导航到腾讯云验证码产品页...")
            await solver.navigate_to_page("https://cloud.tencent.com/product/captcha")
            await asyncio.sleep(3)
            
            # 点击触发按钮
            print("👆 点击'立即体验'按钮...")
            experience_button = await solver.page.query_selector('#captcha_click')
            if experience_button:
                await experience_button.click()
                print("✅ 按钮点击成功")
            else:
                print("❌ 未找到按钮")
                return False
            
            # 使用完整的 solve_captcha 方法
            print("\n🧩 开始解决验证码...")
            print("   (这将自动完成所有步骤：检测、提取、识别、拖拽)")
            
            success = await solver.solve_captcha(max_retries=3)
            
            if success:
                print("🎉 验证码解决成功！")
                
                # 截图保存成功状态
                try:
                    if hasattr(solver, 'main_page') and solver.main_page:
                        await solver.main_page.screenshot(path="final_test_success.png")
                    else:
                        await solver.page.screenshot(path="final_test_success.png")
                    print("📸 成功截图: final_test_success.png")
                except Exception as screenshot_error:
                    print(f"⚠️  截图失败: {screenshot_error}")
                
                return True
            else:
                print("❌ 验证码解决失败")
                
                # 截图保存失败状态
                try:
                    if hasattr(solver, 'main_page') and solver.main_page:
                        await solver.main_page.screenshot(path="final_test_failed.png")
                    else:
                        await solver.page.screenshot(path="final_test_failed.png")
                    print("📸 失败截图: final_test_failed.png")
                except Exception as screenshot_error:
                    print(f"⚠️  截图失败: {screenshot_error}")
                
                return False
                
        except Exception as e:
            print(f"💥 测试过程中出现错误: {e}")
            
            # 错误截图
            try:
                await solver.page.screenshot(path="final_test_error.png")
                print("📸 错误截图: final_test_error.png")
            except:
                pass
            
            return False


async def manual_step_test():
    """手动步骤测试（用于调试）。"""
    print("\n🔧 手动步骤测试（调试模式）")
    print("=" * 50)
    
    solver = TencentSliderSolver(
        dddocr_base_url="http://************:7777",
        headless=False,
        browser_type="chromium"
    )
    
    async with solver:
        try:
            # 导航到页面
            await solver.navigate_to_page("https://cloud.tencent.com/product/captcha")
            await asyncio.sleep(3)
            
            # 点击按钮
            experience_button = await solver.page.query_selector('#captcha_click')
            if experience_button:
                await experience_button.click()
                print("✅ 按钮点击成功")
            
            # 等待验证码
            print("⏳ 等待验证码出现...")
            captcha_appeared = await solver.wait_for_captcha(timeout=30000)
            
            if captcha_appeared:
                print("✅ 验证码检测成功")
                print(f"   主页面类型: {type(getattr(solver, 'main_page', None)).__name__}")
                print(f"   当前页面类型: {type(solver.page).__name__}")
                
                # 提取图片
                print("📷 提取验证码图片...")
                bg_data, slider_data = await solver.get_captcha_images()
                
                if bg_data and slider_data:
                    print(f"✅ 图片提取成功: bg={len(bg_data)}字节, slider={len(slider_data)}字节")
                    
                    # 调用识别
                    print("🧩 调用 dddocr API...")
                    try:
                        target_x = solver.dddocr_client.capcode(slider_data, bg_data)
                        print(f"✅ 识别成功: x={target_x}")
                        
                        # 执行拖拽
                        print(f"🖱️  执行拖拽到位置 x={target_x}...")
                        drag_success = await solver.drag_slider(target_x)
                        
                        if drag_success:
                            print("✅ 拖拽成功")
                            
                            # 等待结果
                            await asyncio.sleep(3)
                            
                            # 检查结果
                            try:
                                success_element = await solver.page.query_selector('.tc-success')
                                error_element = await solver.page.query_selector('.tc-error')
                                
                                if success_element:
                                    print("🎉 验证成功！")
                                    return True
                                elif error_element:
                                    print("❌ 验证失败")
                                    return False
                                else:
                                    print("⚠️  验证结果未知")
                                    return False
                            except Exception as result_error:
                                print(f"⚠️  检查结果时出错: {result_error}")
                                return False
                        else:
                            print("❌ 拖拽失败")
                            return False
                            
                    except Exception as api_error:
                        print(f"❌ API 调用失败: {api_error}")
                        return False
                else:
                    print("❌ 图片提取失败")
                    return False
            else:
                print("❌ 验证码检测失败")
                return False
                
        except Exception as e:
            print(f"💥 手动测试出错: {e}")
            return False


async def main():
    """主函数。"""
    print("腾讯滑块验证码最终测试")
    print("=" * 50)
    
    try:
        print("选择测试模式:")
        print("1. 完整自动测试（推荐）")
        print("2. 手动步骤测试（调试）")
        
        choice = input("请选择 (1-2): ").strip()
        
        if choice == "1":
            print("\n准备开始完整自动测试...")
            print("请确保:")
            print("1. dddocr 服务正在运行 (http://************:7777)")
            print("2. 网络连接正常")
            print()
            
            input("按回车键开始测试...")
            
            success = await final_test()
            
        elif choice == "2":
            print("\n准备开始手动步骤测试...")
            input("按回车键开始测试...")
            
            success = await manual_step_test()
            
        else:
            print("无效选择")
            return
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 测试结果: 成功！")
            print("腾讯滑块验证码解决方案工作正常")
            print("所有修复都已生效：")
            print("✅ iframe 处理")
            print("✅ base64 编码")
            print("✅ /capcode API")
            print("✅ 坐标转换")
            print("✅ 拖拽执行")
        else:
            print("❌ 测试结果: 失败")
            print("请检查错误信息和截图文件")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
    
    print("\n测试结束")


if __name__ == "__main__":
    asyncio.run(main())
