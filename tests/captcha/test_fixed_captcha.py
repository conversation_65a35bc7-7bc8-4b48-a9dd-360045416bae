"""测试修复后的腾讯滑块验证码。

验证 iframe 处理和 base64 编码修复是否正常工作。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.common.utils import logging_utils
from src.captcha.recognizer.t_sec.tencent_slider_solver import TencentSliderSolver


async def test_fixed_captcha():
    """测试修复后的验证码处理。"""
    print("🔧 测试修复后的腾讯滑块验证码")
    print("=" * 50)
    print("修复内容:")
    print("1. ✅ iframe 检测和切换")
    print("2. ✅ 主页面和 iframe 坐标转换")
    print("3. ✅ base64 图片编码")
    print("4. ✅ 增强的错误处理")
    print("=" * 50)
    
    solver = TencentSliderSolver(
        dddocr_base_url="http://10.168.1.201:7777",
        headless=False,
        browser_type="chromium"
    )
    
    async with solver:
        try:
            # 步骤1: 导航到页面
            print("\n📍 步骤1: 导航到腾讯云验证码产品页...")
            await solver.navigate_to_page("https://cloud.tencent.com/product/captcha")
            await asyncio.sleep(3)
            print("✅ 页面加载完成")
            
            # 步骤2: 点击触发按钮
            print("\n👆 步骤2: 点击'立即体验'按钮...")
            experience_button = await solver.page.query_selector('#captcha_click')
            if experience_button:
                await experience_button.click()
                print("✅ 按钮点击成功")
            else:
                print("❌ 未找到按钮")
                return False
            
            # 步骤3: 等待验证码出现（使用修复后的方法）
            print("\n⏳ 步骤3: 等待验证码出现（iframe 检测）...")
            print("   (这可能需要几秒钟...)")
            
            captcha_appeared = await solver.wait_for_captcha(timeout=30000)
            
            if captcha_appeared:
                print("✅ 验证码检测成功！")
                print(f"   主页面: {type(solver.main_page).__name__}")
                print(f"   iframe页面: {type(solver.page).__name__}")
            else:
                print("❌ 验证码检测失败")
                return False
            
            # 步骤4: 测试图片提取
            print("\n📷 步骤4: 提取验证码图片...")
            bg_data, slider_data = await solver.get_captcha_images()
            
            if bg_data and slider_data:
                print("✅ 图片提取成功")
                print(f"   背景图: {len(bg_data)} 字节")
                print(f"   滑块图: {len(slider_data)} 字节")
            else:
                print("❌ 图片提取失败")
                return False
            
            # 步骤5: 测试 dddocr 识别（base64 编码）
            print("\n🧩 步骤5: 测试验证码识别...")
            try:
                from src.captcha.recognizer.t_sec.dddocr_client import DDDOCRClient
                
                client = DDDOCRClient("http://10.168.1.201:7777")
                
                # 测试 base64 编码
                import base64
                bg_b64 = base64.b64encode(bg_data).decode('utf-8')
                slider_b64 = base64.b64encode(slider_data).decode('utf-8')
                
                print(f"   背景图 base64 长度: {len(bg_b64)}")
                print(f"   滑块图 base64 长度: {len(slider_b64)}")
                
                # 调用识别 API - 使用 capcode 方法
                x = client.capcode(slider_data, bg_data)
                
                print(f"✅ 识别成功，目标位置: x={x}")
                
            except Exception as e:
                print(f"❌ 识别失败: {e}")
                return False
            
            # 步骤6: 测试拖拽（坐标转换）
            print(f"\n🖱️  步骤6: 测试拖拽到位置 x={x}...")
            
            try:
                success = await solver.drag_slider(x)
                
                if success:
                    print("✅ 拖拽执行成功")
                    
                    # 等待验证结果
                    print("\n⏳ 等待验证结果...")
                    await asyncio.sleep(3)
                    
                    # 检查验证结果
                    try:
                        success_element = await solver.page.query_selector('.tc-success')
                        error_element = await solver.page.query_selector('.tc-error')
                        
                        if success_element:
                            print("🎉 验证码解决成功！")
                            
                            # 截图保存成功状态
                            await solver.main_page.screenshot(path="captcha_success_fixed.png")
                            print("📸 成功截图: captcha_success_fixed.png")
                            
                            return True
                        elif error_element:
                            print("❌ 验证码解决失败")
                            
                            # 截图保存失败状态
                            await solver.main_page.screenshot(path="captcha_failed_fixed.png")
                            print("📸 失败截图: captcha_failed_fixed.png")
                            
                            return False
                        else:
                            print("⚠️  验证结果未知")
                            
                            # 截图保存未知状态
                            await solver.main_page.screenshot(path="captcha_unknown_fixed.png")
                            print("📸 未知状态截图: captcha_unknown_fixed.png")
                            
                            return False
                    except Exception as result_error:
                        print(f"⚠️  无法检查验证结果: {result_error}")
                        return False
                else:
                    print("❌ 拖拽执行失败")
                    return False
                    
            except Exception as drag_error:
                print(f"❌ 拖拽过程出错: {drag_error}")
                return False
                
        except Exception as e:
            print(f"💥 测试过程中出现错误: {e}")
            
            # 错误截图
            try:
                await solver.page.screenshot(path="error_fixed_test.png")
                print("📸 错误截图: error_fixed_test.png")
            except:
                pass
            
            return False


async def main():
    """主函数。"""
    print("修复后的腾讯滑块验证码测试")
    print("=" * 50)
    
    try:
        print("准备开始测试...")
        print("请确保:")
        print("1. dddocr 服务正在运行 (http://10.168.1.201:7777)")
        print("2. 网络连接正常")
        print()
        
        input("按回车键开始测试...")
        
        success = await test_fixed_captcha()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 测试结果: 成功！")
            print("所有修复都正常工作：")
            print("✅ iframe 检测和切换")
            print("✅ 图片提取和 base64 编码")
            print("✅ 坐标转换和拖拽")
            print("✅ 验证码解决")
        else:
            print("❌ 测试结果: 失败")
            print("请检查上面的错误信息和截图")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
    
    print("\n测试结束")


if __name__ == "__main__":
    asyncio.run(main())
