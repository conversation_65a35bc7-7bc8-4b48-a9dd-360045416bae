# save_as: capture_tc_fg_item_robust_click.py
import html
import io
import os
import re
import time

from PIL import Image
from playwright.sync_api import sync_playwright, TimeoutError as PWTimeout


# ----------------- 辅助函数 -----------------
def parse_style(style: str):
    s = html.unescape(style or "")
    res = {}
    m = re.search(r'background-image\s*:\s*url\((["\']?)(.*?)\1\)', s, flags=re.I)
    res['background_image'] = m.group(2) if m else None
    m = re.search(r'background-position\s*:\s*([-+]?\d+(\.\d+)?)px\s+([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    if m:
        res['bg_pos_x'] = float(m.group(1))
        res['bg_pos_y'] = float(m.group(3))
    else:
        res['bg_pos_x'] = res['bg_pos_y'] = None
    m = re.search(r'background-size\s*:\s*([-+]?\d+(\.\d+)?)px\s+([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    if m:
        res['bg_size_w'] = float(m.group(1))
        res['bg_size_h'] = float(m.group(3))
    else:
        res['bg_size_w'] = res['bg_size_h'] = None
    m = re.search(r'\bwidth\s*:\s*([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    res['style_width'] = float(m.group(1)) if m else None
    m = re.search(r'\bheight\s*:\s*([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    res['style_height'] = float(m.group(1)) if m else None
    return res

def crop_from_background(image_bytes, css_bg_w, css_bg_h, bg_pos_x, bg_pos_y, elem_w_css, elem_h_css):
    img = Image.open(io.BytesIO(image_bytes)).convert("RGBA")
    actual_w, actual_h = img.size
    if css_bg_w is None or css_bg_h is None:
        css_bg_w, css_bg_h = actual_w, actual_h
    sx = actual_w / float(css_bg_w)
    sy = actual_h / float(css_bg_h)
    start_x = abs(bg_pos_x) * sx
    start_y = abs(bg_pos_y) * sy
    crop_w = elem_w_css * sx
    crop_h = elem_h_css * sy
    left = int(round(max(0, start_x)))
    upper = int(round(max(0, start_y)))
    right = int(round(min(actual_w, left + crop_w)))
    lower = int(round(min(actual_h, upper + crop_h)))
    if right <= left or lower <= upper:
        raise RuntimeError(f"空裁切框: {(left,upper,right,lower)} img_size={(actual_w,actual_h)}")
    return img.crop((left, upper, right, lower))

def robust_click(page, selector, max_attempts=6, wait_between=0.8):
    """
    多策略点击：标准 click -> 强制 click -> JS click -> 派发事件
    返回 True if click likely executed (no exception), else False
    """
    locator = page.locator(selector)
    for attempt in range(1, max_attempts + 1):
        try:
            # 等待 selector 可见（短超时）
            locator.wait_for(state="visible", timeout=3000)
        except Exception:
            # 继续尝试多种方法即便不可见
            pass

        # 1) 常规 click（优先）
        try:
            locator.click(timeout=4000)
            return True
        except Exception:
            pass

        # 2) 强制点击（force），并先滚动到该元素
        try:
            page.evaluate("(sel)=>{const e=document.querySelector(sel); if(e){e.scrollIntoView({block:'center',inline:'center'});} }", selector)
            locator.click(timeout=3000, force=True)
            return True
        except Exception:
            pass

        # 3) JS 原生 click()
        try:
            page.evaluate("(sel)=>{const e=document.querySelector(sel); if(e){e.click(); return true;} return false;}", selector)
            # small wait to let DOM react
            page.wait_for_timeout(300)
            return True
        except Exception:
            pass

        # 4) 派发鼠标事件（mousedown mouseup click）
        try:
            page.evaluate(
                """(sel)=>{
                    const e=document.querySelector(sel);
                    if(!e) return false;
                    function dispatch(name){
                        const ev = new MouseEvent(name, {bubbles:true,cancelable:true,view:window});
                        e.dispatchEvent(ev);
                    }
                    dispatch('mouseover');
                    dispatch('mousedown');
                    dispatch('mouseup');
                    dispatch('click');
                    return true;
                }""",
                selector)
            page.wait_for_timeout(300)
            return True
        except Exception:
            pass

        # 5) 通过焦点 + Enter（有时按钮响应键盘）
        try:
            page.focus(selector)
            page.keyboard.press("Enter")
            page.wait_for_timeout(300)
            return True
        except Exception:
            pass

        # 等待再试
        time.sleep(wait_between)
    # 全部失败
    return False

# ----------------- 主流程 -----------------
def main(save_path="cropped_result.png", headless=False):
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=headless)
        context = browser.new_context()
        page = context.new_page()

        page.goto("https://cloud.tencent.com/product/captcha", wait_until="domcontentloaded", timeout=30000)
        # 向下滚动一段距离，确保“立即体验”可见
        page.evaluate("() => window.scrollBy(0, window.innerHeight / 2)")
        time.sleep(0.5)
        # 尝试点击 #captcha_click（更稳健）
        print("等待并点击“立即体验”按钮...")
        CLICK_SELECTOR="#captcha_click"
        try:
            page.wait_for_selector(CLICK_SELECTOR, timeout=5000)
            page.click(CLICK_SELECTOR)
            print("点击完成。")
        except PWTimeout:
            print("未能在超时内找到立即体验按钮，尝试直接点击（若可见）...")
            try:
                page.click(CLICK_SELECTOR, timeout=2000)
            except Exception as e_click:
                print("点击失败：", e_click)
                # 继续尝试查找 iframe（可能页面结构不同）

        # 给 iframe 加载时间并等待至少出现一个 iframe
        try:
            page.wait_for_selector("iframe", timeout=10000)
        except Exception:
            # 若没有 iframe，继续，但我们会后续检查 frames
            pass

        # 等待短时间以让 frame 内资源加载
        page.wait_for_timeout(1200)

        # 在 frames 中查找 target element（带条件：background-position 两值 < 0）
        target_element = None
        parsed_meta = None
        target_frame = None

        # 给出较长的重试周期（因为 iframe 可能异步加载）
        for round_i in range(0, 10):
            for fr in page.frames:
                try:
                    # 尝试在 frame 中找所有符合的元素（避免只取第一个）
                    els = fr.query_selector_all("div.tc-fg-item")
                    if not els:
                        continue
                    for el in els:
                        style = el.get_attribute("style") or ""
                        parsed = parse_style(style)
                        bx = parsed.get("bg_pos_x")
                        by = parsed.get("bg_pos_y")
                        if bx is None or by is None:
                            continue
                        # 只取两个值均小于 0 的元素
                        if bx < 0 and by < 0:
                            target_element = el
                            parsed_meta = parsed
                            target_frame = fr
                            break
                    if target_element:
                        break
                except Exception:
                    continue
            if target_element:
                break
            page.wait_for_timeout(800)

        if not target_element:
            browser.close()
            raise RuntimeError("未找到满足条件（background-position 两值均小于0）的 div.tc-fg-item。")

        # 获取元素显示尺寸（优先 bounding_box）
        bbox = None
        try:
            bbox = target_element.bounding_box()
        except Exception:
            bbox = None
        if bbox and bbox.get("width") and bbox.get("height"):
            elem_w = bbox["width"]
            elem_h = bbox["height"]
        else:
            elem_w = parsed_meta.get("style_width") or 60
            elem_h = parsed_meta.get("style_height") or 60

        bg_url = parsed_meta.get("background_image")
        bg_pos_x = parsed_meta.get("bg_pos_x")
        bg_pos_y = parsed_meta.get("bg_pos_y")
        bg_size_w = parsed_meta.get("bg_size_w")
        bg_size_h = parsed_meta.get("bg_size_h")

        if not bg_url:
            browser.close()
            raise RuntimeError("无法解析 background-image URL。")

        # 使用 Playwright context.request 来下载图片（携带会话 cookie）
        print("背景图 URL：", bg_url)
        resp = context.request.get(bg_url, timeout=30000)
        if resp.status != 200:
            # 若失败则尝试简单重试一次
            resp = context.request.get(bg_url, timeout=30000)
            if resp.status != 200:
                browser.close()
                raise RuntimeError(f"下载背景图失败，HTTP status={resp.status}")
        img_bytes = resp.body()

        # 裁切并保存
        cropped = crop_from_background(img_bytes, bg_size_w, bg_size_h, bg_pos_x, bg_pos_y, elem_w, elem_h)
        os.makedirs(os.path.dirname(save_path) or ".", exist_ok=True)
        cropped.save(save_path, format="PNG")
        print(f"已保存裁切结果到 {save_path}")

        browser.close()

if __name__ == "__main__":
    # 改 headless=True 在服务器上无头运行
    main(save_path="cropped_result.png", headless=False)
