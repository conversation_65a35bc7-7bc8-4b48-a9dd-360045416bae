"""测试 dddocr 服务连接和 API 功能。

验证 dddocr 服务是否正常运行并测试所有 API 端点。
"""

import sys
from pathlib import Path
import io
from PIL import Image, ImageDraw
import base64

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.common.utils import logging_utils
from src.captcha.recognizer.t_sec.dddocr_client import DDDOCRClient


def create_test_image(width=300, height=150, text="TEST"):
    """创建测试图片。"""
    img = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(img)
    
    # 绘制一些测试内容
    draw.rectangle([10, 10, width-10, height-10], outline='black', width=2)
    draw.text((width//2-20, height//2-10), text, fill='black')
    
    # 转换为字节
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    return img_bytes.getvalue()


def create_slider_test_images():
    """创建滑块测试图片。"""
    # 背景图 - 带缺口
    bg_img = Image.new('RGB', (300, 150), color='lightblue')
    draw = ImageDraw.Draw(bg_img)
    draw.rectangle([10, 10, 290, 140], outline='darkblue', width=2)
    # 绘制缺口
    draw.rectangle([100, 50, 150, 100], fill='white', outline='red', width=2)
    draw.text((120, 70), "缺口", fill='red')
    
    # 滑块图
    slider_img = Image.new('RGB', (50, 50), color='lightgreen')
    draw = ImageDraw.Draw(slider_img)
    draw.rectangle([0, 0, 49, 49], outline='darkgreen', width=2)
    draw.text((15, 20), "滑块", fill='darkgreen')
    
    # 转换为字节
    bg_bytes = io.BytesIO()
    bg_img.save(bg_bytes, format='PNG')
    
    slider_bytes = io.BytesIO()
    slider_img.save(slider_bytes, format='PNG')
    
    return bg_bytes.getvalue(), slider_bytes.getvalue()


def test_dddocr_connection():
    """测试 dddocr 服务连接。"""
    print("🔗 测试 dddocr 服务连接")
    print("=" * 50)
    
    # 创建客户端
    client = DDDOCRClient("http://10.168.1.201:7777")
    
    try:
        # 测试服务是否可达
        print("📡 检查服务连接...")
        if client.test_connection():
            print("✅ dddocr 服务连接正常")
        else:
            print("❌ dddocr 服务连接失败")
            return False
        
        # 创建测试图片
        print("\n🖼️  创建测试图片...")
        bg_data, slider_data = create_slider_test_images()
        text_img_data = create_test_image(200, 100, "Hello123")
        
        print("✅ 测试图片创建完成")
        
        # 测试各个 API 端点
        print("\n🧪 测试 API 端点...")
        
        # 1. 测试 slideComparison
        print("\n1. 测试 /slideComparison 端点...")
        try:
            x = client.slide_comparison(slider_data, bg_data)
            print(f"✅ slideComparison 成功: x={x}")
        except Exception as e:
            print(f"❌ slideComparison 失败: {e}")
        
        # 2. 测试 capcode
        print("\n2. 测试 /capcode 端点...")
        try:
            x = client.capcode(slider_data, bg_data)
            print(f"✅ capcode 成功: x={x}")
        except Exception as e:
            print(f"❌ capcode 失败: {e}")
        
        # 3. 测试 classification
        print("\n3. 测试 /classification 端点...")
        try:
            text = client.classification(text_img_data)
            print(f"✅ classification 成功: '{text}'")
        except Exception as e:
            print(f"❌ classification 失败: {e}")
        
        # 4. 测试 detection
        print("\n4. 测试 /detection 端点...")
        try:
            objects = client.detection(text_img_data)
            print(f"✅ detection 成功: 检测到 {len(objects)} 个对象")
            for i, obj in enumerate(objects[:3]):  # 只显示前3个
                print(f"   对象{i+1}: {obj}")
        except Exception as e:
            print(f"❌ detection 失败: {e}")
        
        # 5. 测试 calculate
        print("\n5. 测试 /calculate 端点...")
        try:
            # 创建包含数学表达式的图片
            calc_img_data = create_test_image(150, 80, "2+3=?")
            result = client.calculate(calc_img_data)
            print(f"✅ calculate 成功: '{result}'")
        except Exception as e:
            print(f"❌ calculate 失败: {e}")
        
        # 6. 测试 select
        print("\n6. 测试 /select 端点...")
        try:
            selections = client.select(text_img_data)
            print(f"✅ select 成功: 识别到 {len(selections)} 个选择项")
            for key, value in list(selections.items())[:3]:  # 只显示前3个
                print(f"   '{key}': {value}")
        except Exception as e:
            print(f"❌ select 失败: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 dddocr 服务测试完成！")
        print("所有 API 端点都已测试")
        
        return True
        
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
        return False
    
    finally:
        client.close()


def test_image_formats():
    """测试不同图片格式的支持。"""
    print("\n📷 测试图片格式支持")
    print("=" * 30)
    
    client = DDDOCRClient("http://10.168.1.201:7777")
    
    try:
        # 测试不同格式的图片
        formats = ['PNG', 'JPEG', 'BMP']
        
        for fmt in formats:
            print(f"\n测试 {fmt} 格式...")
            
            # 创建指定格式的图片
            img = Image.new('RGB', (100, 50), color='white')
            draw = ImageDraw.Draw(img)
            draw.text((10, 20), f"{fmt}123", fill='black')
            
            img_bytes = io.BytesIO()
            img.save(img_bytes, format=fmt)
            img_data = img_bytes.getvalue()
            
            try:
                text = client.classification(img_data)
                print(f"✅ {fmt} 格式支持: '{text}'")
            except Exception as e:
                print(f"❌ {fmt} 格式失败: {e}")
    
    except Exception as e:
        print(f"格式测试出错: {e}")
    
    finally:
        client.close()


def main():
    """主函数。"""
    print("dddocr 服务连接测试工具")
    print("=" * 50)
    print("测试服务地址: http://10.168.1.201:7777")
    print("=" * 50)
    
    try:
        # 基础连接测试
        success = test_dddocr_connection()
        
        if success:
            # 图片格式测试
            test_image_formats()
            
            print("\n" + "=" * 50)
            print("🎉 所有测试完成！")
            print("dddocr 服务工作正常，可以开始使用验证码识别功能")
        else:
            print("\n" + "=" * 50)
            print("❌ 测试失败！")
            print("请检查:")
            print("1. dddocr 服务是否正在运行")
            print("2. 服务地址是否正确: http://10.168.1.201:7777")
            print("3. 网络连接是否正常")
        
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
    
    print("\n测试结束")


if __name__ == "__main__":
    main()
