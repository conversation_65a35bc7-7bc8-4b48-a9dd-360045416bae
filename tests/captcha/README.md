# 腾讯滑块验证码测试

这个目录包含了腾讯滑块验证码识别系统的测试脚本。

## 测试文件说明

### 1. `quick_test_tencent.py` - 快速测试（推荐）
专门用于测试腾讯云验证码产品页面的"立即体验"功能。

**特点：**
- 直接测试 `https://cloud.tencent.com/product/captcha` 页面
- 自动查找并点击"立即体验"按钮 (`#captcha_click`)
- 智能等待验证码出现
- 自动解决滑块验证码
- 详细的步骤提示和错误处理
- 自动截图保存测试结果

**运行方法：**
```bash
cd tests/captcha
python quick_test_tencent.py
```

### 2. `test_tencent_captcha.py` - 完整测试套件
包含多种测试模式的完整测试工具。

**测试模式：**
1. 腾讯云验证码产品页测试
2. 自定义页面测试
3. 图片提取测试
4. 全部测试

**运行方法：**
```bash
cd tests/captcha
python test_tencent_captcha.py
```

### 3. `test_dddocr_connection.py` - dddocr 服务测试
测试 dddocr 服务连接和所有 API 端点。

**测试内容：**
- 服务连接测试
- `/slideComparison` 端点测试
- `/capcode` 端点测试
- `/classification` 端点测试
- `/detection` 端点测试
- `/calculate` 端点测试
- `/select` 端点测试
- 图片格式支持测试

**运行方法：**
```bash
cd tests/captcha
python test_dddocr_connection.py
```

## 测试前准备

### 1. 确保 dddocr 服务运行
```bash
# 确保 dddocr 服务在 http://************:7777 运行
curl http://************:7777/health  # 检查服务状态
```

### 2. 安装依赖
```bash
# 安装 playwright 浏览器
playwright install chromium

# 确保已安装所需的 Python 包
pip install playwright requests pillow
```

### 3. 网络连接
确保能够访问：
- `http://************:7777` (dddocr 服务)
- `https://cloud.tencent.com/product/captcha` (测试页面)

## 推荐测试流程

### 第一步：测试 dddocr 服务
```bash
cd tests/captcha
python test_dddocr_connection.py
```
确保所有 API 端点都正常工作。

### 第二步：快速测试腾讯验证码
```bash
cd tests/captcha
python quick_test_tencent.py
```
这会自动测试腾讯云产品页的滑块验证码。

### 第三步：完整测试（可选）
```bash
cd tests/captcha
python test_tencent_captcha.py
```
运行完整的测试套件。

## 测试结果

### 成功指标
- ✅ dddocr 服务连接正常
- ✅ 能够找到"立即体验"按钮
- ✅ 验证码成功出现
- ✅ 滑块验证码解决成功
- ✅ 生成成功截图

### 失败排查
如果测试失败，请检查：

1. **dddocr 服务问题**
   - 服务是否运行在正确端口
   - API 端点是否响应正常
   - 网络连接是否正常

2. **页面结构变化**
   - 腾讯云页面结构是否发生变化
   - "立即体验"按钮选择器是否仍然有效
   - 验证码 HTML 结构是否改变

3. **浏览器问题**
   - Playwright 浏览器是否正确安装
   - 浏览器是否能正常启动
   - 页面是否能正常加载

## 截图文件说明

测试过程中会生成以下截图文件：

- `captcha_initial.png` - 验证码初始状态
- `captcha_success.png` - 验证码解决成功
- `captcha_failed.png` - 验证码解决失败
- `no_captcha_debug.png` - 验证码未出现时的调试截图
- `error_debug.png` - 出现错误时的调试截图

这些截图可以帮助调试问题和验证测试结果。

## 注意事项

1. **测试频率**：不要过于频繁地测试，避免被网站限制
2. **网络环境**：确保网络连接稳定
3. **服务状态**：测试前确认 dddocr 服务正常运行
4. **浏览器版本**：使用最新版本的 Playwright 和浏览器
5. **权限问题**：确保有写入截图文件的权限

## 故障排除

### 常见错误及解决方案

1. **ImportError: attempted relative import with no known parent package**
   - 确保从项目根目录运行测试
   - 使用 `cd tests/captcha && python test_name.py` 的方式运行

2. **连接超时**
   - 检查 dddocr 服务是否运行
   - 检查网络连接
   - 尝试增加超时时间

3. **验证码未出现**
   - 检查页面结构是否变化
   - 尝试手动触发验证码
   - 查看调试截图

4. **验证码解决失败**
   - 检查 dddocr API 是否正常
   - 尝试调整识别参数
   - 查看错误日志

如有其他问题，请查看详细的错误日志和截图文件进行调试。
