"""腾讯滑块验证码专用解决器。

专门针对腾讯滑块验证码的识别和操作实现。
"""

import asyncio
import html
import logging
import re
import time
from typing import Optional, Tuple, Dict, Any
from urllib.parse import urlparse, parse_qs
import base64
import io

from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from PIL import Image
import requests

from common.utils import logging_utils
from .dddocr_client import DDDOCRClient

logger = logging.getLogger(__name__)


def _parse_style(style: str) -> Dict[str, Any]:
    """解析CSS样式字符串，提取背景图片相关属性。

    Args:
        style: CSS样式字符串

    Returns:
        包含解析结果的字典
    """
    s = html.unescape(style or "")
    res = {}

    # 提取 background-image URL
    m = re.search(r'background-image\s*:\s*url\((["\']?)(.*?)\1\)', s, flags=re.I)
    res['background_image'] = m.group(2) if m else None

    # 提取 background-position
    m = re.search(r'background-position\s*:\s*([-+]?\d+(\.\d+)?)px\s+([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    if m:
        res['bg_pos_x'] = float(m.group(1))
        res['bg_pos_y'] = float(m.group(3))
    else:
        res['bg_pos_x'] = res['bg_pos_y'] = None

    # 提取 background-size
    m = re.search(r'background-size\s*:\s*([-+]?\d+(\.\d+)?)px\s+([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    if m:
        res['bg_size_w'] = float(m.group(1))
        res['bg_size_h'] = float(m.group(3))
    else:
        res['bg_size_w'] = res['bg_size_h'] = None

    # 提取 width 和 height
    m = re.search(r'\bwidth\s*:\s*([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    res['style_width'] = float(m.group(1)) if m else None

    m = re.search(r'\bheight\s*:\s*([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    res['style_height'] = float(m.group(1)) if m else None

    return res


def _crop_from_background(image_bytes: bytes, css_bg_w: float, css_bg_h: float,
                         bg_pos_x: float, bg_pos_y: float, elem_w_css: float, elem_h_css: float) -> Image.Image:
    """从背景图片中裁剪出滑块部分。

    Args:
        image_bytes: 背景图片字节数据
        css_bg_w: CSS中定义的背景图片宽度
        css_bg_h: CSS中定义的背景图片高度
        bg_pos_x: 背景位置X坐标
        bg_pos_y: 背景位置Y坐标
        elem_w_css: 元素CSS宽度
        elem_h_css: 元素CSS高度

    Returns:
        裁剪后的滑块图片
    """
    img = Image.open(io.BytesIO(image_bytes)).convert("RGBA")
    actual_w, actual_h = img.size

    # 如果没有指定背景尺寸，使用实际图片尺寸
    if css_bg_w is None or css_bg_h is None:
        css_bg_w, css_bg_h = actual_w, actual_h

    # 计算缩放比例
    sx = actual_w / float(css_bg_w)
    sy = actual_h / float(css_bg_h)

    # 计算裁剪区域
    start_x = abs(bg_pos_x) * sx
    start_y = abs(bg_pos_y) * sy
    crop_w = elem_w_css * sx
    crop_h = elem_h_css * sy

    # 确保裁剪区域在图片范围内
    left = int(round(max(0, start_x)))
    upper = int(round(max(0, start_y)))
    right = int(round(min(actual_w, left + crop_w)))
    lower = int(round(min(actual_h, upper + crop_h)))

    if right <= left or lower <= upper:
        raise RuntimeError(f"空裁切框: {(left,upper,right,lower)} img_size={(actual_w,actual_h)}")

    return img.crop((left, upper, right, lower))


class TencentSliderSolver:
    """腾讯滑块验证码解决器。
    
    专门用于解决腾讯滑块验证码的自动识别和操作。
    """
    
    def __init__(self, 
                 dddocr_base_url: str = "http://10.168.1.201:7777",
                 headless: bool = False,
                 browser_type: str = "chromium"):
        """初始化腾讯滑块验证码解决器。
        
        Args:
            dddocr_base_url: dddocr服务的基础URL
            headless: 是否使用无头模式运行浏览器
            browser_type: 浏览器类型
        """
        self.dddocr_client = DDDOCRClient(dddocr_base_url)
        self.headless = headless
        self.browser_type = browser_type
        
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        logging_utils.logger_print(
            msg="initialized tencent slider solver",
            custom_logger=logger
        )
    
    async def __aenter__(self):
        """异步上下文管理器入口。"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口。"""
        await self.close()
    
    async def start(self):
        """启动浏览器。"""
        self.playwright = await async_playwright().start()
        
        if self.browser_type == "firefox":
            browser_launcher = self.playwright.firefox
        elif self.browser_type == "webkit":
            browser_launcher = self.playwright.webkit
        else:
            browser_launcher = self.playwright.chromium
        
        self.browser = await browser_launcher.launch(
            headless=self.headless,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security'
            ]
        )
        
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        self.page = await self.context.new_page()
        
        logging_utils.logger_print(
            msg="tencent slider solver browser started",
            custom_logger=logger
        )
    
    async def close(self):
        """关闭浏览器。"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
        if self.dddocr_client:
            self.dddocr_client.close()
    
    async def navigate_to_page(self, url: str):
        """导航到目标页面。"""
        if not self.page:
            raise RuntimeError("browser not started")

        await self.page.goto(url, wait_until='domcontentloaded')
        await self.page.wait_for_timeout(2000)

        # 向下滚动以确保"立即体验"按钮可见
        logging_utils.logger_print(
            msg="scrolling page to make experience button visible",
            custom_logger=logger
        )
        await self.page.evaluate("() => window.scrollBy(0, window.innerHeight / 2)")
        await self.page.wait_for_timeout(500)
    
    async def wait_for_captcha(self, timeout: int = 30000) -> bool:
        """等待验证码出现。

        Args:
            timeout: 等待超时时间（毫秒）

        Returns:
            是否成功检测到验证码
        """
        try:
            logging_utils.logger_print(
                msg="waiting for tencent captcha to appear",
                custom_logger=logger
            )

            # 首先等待 iframe 出现（腾讯验证码通常在 iframe 中）
            try:
                await self.page.wait_for_selector('#tcaptcha_iframe_dy', timeout=timeout)
                logging_utils.logger_print(
                    msg="found captcha iframe",
                    custom_logger=logger
                )

                # 获取 iframe 元素
                iframe_element = await self.page.query_selector('#tcaptcha_iframe_dy')
                if iframe_element:
                    # 切换到 iframe 内容
                    iframe_content = await iframe_element.content_frame()
                    if iframe_content:
                        # 等待 iframe 内的验证码元素
                        await iframe_content.wait_for_selector('.tc-opera', timeout=15000)

                        # 保存原始页面和 iframe 内容
                        self.main_page = self.page
                        self.iframe_page = iframe_content
                        self.page = iframe_content

                        logging_utils.logger_print(
                            msg="successfully switched to captcha iframe",
                            custom_logger=logger
                        )

                        # 等待背景图和滑块加载完成
                        await self.page.wait_for_selector('.tc-bg-img', timeout=5000)
                        await self.page.wait_for_selector('.tc-slider-normal', timeout=5000)

                        return True

            except Exception as iframe_error:
                logging_utils.logger_print(
                    msg=f"iframe approach failed, trying direct approach: {iframe_error}",
                    custom_logger=logger
                )

            # 如果 iframe 方法失败，尝试直接查找验证码元素
            try:
                await self.page.wait_for_selector('.tc-opera', timeout=timeout)
                await self.page.wait_for_selector('.tc-bg-img', timeout=5000)
                await self.page.wait_for_selector('.tc-slider-normal', timeout=5000)

                logging_utils.logger_print(
                    msg="tencent captcha detected (direct approach)",
                    custom_logger=logger
                )
                return True

            except Exception as direct_error:
                logging_utils.logger_print(
                    msg=f"direct approach failed: {direct_error}",
                    custom_logger=logger
                )

            return False

        except Exception as e:
            logging_utils.logger_print(
                msg="failed to detect tencent captcha",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return False
    
    async def get_captcha_images(self) -> Tuple[Optional[bytes], Optional[bytes]]:
        """获取验证码图片。

        使用改进的方法：
        1. 背景图：通过URL下载
        2. 滑块图：通过精确裁剪获取

        Returns:
            (背景图数据, 滑块图数据)
        """
        try:
            # 查找符合条件的滑块元素（background-position 两值均小于0）
            target_element = None
            parsed_meta = None

            logging_utils.logger_print(
                msg="searching for slider element with negative background-position",
                custom_logger=logger
            )

            # 在所有frame中查找（使用主页面的frames）
            main_page = self.main_page if hasattr(self, 'main_page') and self.main_page else self.page
            frames_to_search = []

            # 如果当前page是Frame，则只搜索当前frame
            if hasattr(self.page, 'frames'):
                frames_to_search = self.page.frames
            else:
                # 当前page是Frame对象，只搜索自己
                frames_to_search = [self.page]

            # 如果有主页面，也搜索主页面的frames
            if hasattr(self, 'main_page') and self.main_page and hasattr(self.main_page, 'frames'):
                frames_to_search.extend(self.main_page.frames)

            for frame in frames_to_search:
                try:
                    # 查找所有 tc-fg-item 元素
                    elements = await frame.query_selector_all("div.tc-fg-item")
                    if not elements:
                        continue

                    for element in elements:
                        style = await element.get_attribute("style") or ""
                        parsed = _parse_style(style)

                        bx = parsed.get("bg_pos_x")
                        by = parsed.get("bg_pos_y")

                        if bx is None or by is None:
                            continue

                        # 只选择两个值均小于0的元素
                        if bx < 0 and by < 0:
                            logging_utils.logger_print(
                                msg=f"found target element: bg_pos=({bx}, {by})",
                                custom_logger=logger
                            )
                            target_element = element
                            parsed_meta = parsed
                            break

                    if target_element:
                        break

                except Exception as frame_error:
                    logging_utils.logger_print(
                        msg=f"error searching in frame: {frame_error}",
                        custom_logger=logger
                    )
                    continue

            if not target_element:
                logging_utils.logger_print(
                    msg="no slider element found with negative background-position",
                    custom_logger=logger
                )
                return None, None

            # 获取元素显示尺寸
            bbox = None
            try:
                bbox = await target_element.bounding_box()
            except Exception:
                bbox = None

            if bbox and bbox.get("width") and bbox.get("height"):
                elem_w = bbox["width"]
                elem_h = bbox["height"]
            else:
                elem_w = parsed_meta.get("style_width") or 60
                elem_h = parsed_meta.get("style_height") or 60

            # 获取背景图片信息（从 #slideBg 元素）
            bg_data = None
            bg_url = None

            # 查找背景图元素
            for frame in frames_to_search:
                try:
                    bg_element = await frame.query_selector('#slideBg')
                    if bg_element:
                        bg_style = await bg_element.get_attribute('style')
                        if bg_style:
                            bg_parsed = _parse_style(bg_style)
                            bg_url = bg_parsed.get("background_image")
                            if bg_url:
                                logging_utils.logger_print(
                                    msg=f"found background image URL from #slideBg: {bg_url[:50]}...",
                                    custom_logger=logger
                                )
                                break
                except Exception as bg_search_error:
                    logging_utils.logger_print(
                        msg=f"error searching for background in frame: {bg_search_error}",
                        custom_logger=logger
                    )
                    continue

            if not bg_url:
                logging_utils.logger_print(
                    msg="no background image URL found in #slideBg element",
                    custom_logger=logger
                )
                return None, None

            logging_utils.logger_print(
                msg=f"downloading background image: {bg_url[:50]}...",
                custom_logger=logger
            )

            # 使用 context.request 下载图片（携带会话cookie）
            try:
                response = await self.context.request.get(bg_url, timeout=30000)
                if response.status != 200:
                    # 重试一次
                    response = await self.context.request.get(bg_url, timeout=30000)
                    if response.status != 200:
                        logging_utils.logger_print(
                            msg=f"failed to download background image: HTTP {response.status}",
                            custom_logger=logger
                        )
                        return None, None

                bg_data = await response.body()

                logging_utils.logger_print(
                    msg=f"background image downloaded: {len(bg_data)} bytes",
                    custom_logger=logger
                )

            except Exception as download_error:
                logging_utils.logger_print(
                    msg=f"error downloading background image: {download_error}",
                    custom_logger=logger
                )
                return None, None

            # 获取滑块图片（从滑块元素的背景图裁剪）
            slider_data = None
            slider_bg_url = parsed_meta.get("background_image")
            bg_pos_x = parsed_meta.get("bg_pos_x")
            bg_pos_y = parsed_meta.get("bg_pos_y")
            bg_size_w = parsed_meta.get("bg_size_w")
            bg_size_h = parsed_meta.get("bg_size_h")

            if slider_bg_url:
                logging_utils.logger_print(
                    msg=f"downloading slider background image: {slider_bg_url[:50]}...",
                    custom_logger=logger
                )

                try:
                    # 下载滑块的背景图
                    slider_response = await self.context.request.get(slider_bg_url, timeout=30000)
                    if slider_response.status == 200:
                        slider_bg_data = await slider_response.body()

                        # 裁剪滑块图片
                        cropped_slider = _crop_from_background(
                            slider_bg_data, bg_size_w, bg_size_h, bg_pos_x, bg_pos_y, elem_w, elem_h
                        )

                        # 转换为字节数据
                        buffer = io.BytesIO()
                        cropped_slider.save(buffer, format='PNG')
                        slider_data = buffer.getvalue()

                        logging_utils.logger_print(
                            msg=f"slider image cropped successfully: {len(slider_data)} bytes, size={cropped_slider.size}",
                            custom_logger=logger
                        )
                    else:
                        logging_utils.logger_print(
                            msg=f"failed to download slider background: HTTP {slider_response.status}",
                            custom_logger=logger
                        )

                except Exception as slider_error:
                    logging_utils.logger_print(
                        msg=f"error processing slider image: {slider_error}",
                        custom_logger=logger
                    )
            else:
                logging_utils.logger_print(
                    msg="no slider background image URL found",
                    custom_logger=logger
                )

            return bg_data, slider_data

        except Exception as e:
            logging_utils.logger_print(
                msg="error getting captcha images",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return None, None
    
    def _extract_image_url(self, style: str) -> Optional[str]:
        """从样式中提取图片URL。"""
        if not style:
            return None
        
        # 查找 background-image: url("...") 
        import re
        match = re.search(r'background-image:\s*url\(["\']?([^"\']+)["\']?\)', style)
        if match:
            return match.group(1)
        
        return None
    
    async def _download_image(self, url: str) -> Optional[bytes]:
        """下载图片。"""
        try:
            # 使用页面的会话下载图片以保持cookies
            response = await self.page.evaluate(f"""
                fetch('{url}').then(response => response.arrayBuffer())
            """)
            
            if response:
                return bytes(response)
            
            # 如果页面下载失败，尝试直接下载
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as resp:
                    if resp.status == 200:
                        return await resp.read()
            
            return None
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"error downloading image: {url}",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return None
    
    async def _download_and_crop_slider(self, url: str, style: str) -> Optional[bytes]:
        """下载并裁剪滑块图片。"""
        try:
            # 下载完整图片
            full_image_data = await self._download_image(url)
            if not full_image_data:
                return None

            logging_utils.logger_print(
                msg=f"processing slider image: {url}",
                custom_logger=logger
            )
            logging_utils.logger_print(
                msg=f"element style: {style}",
                custom_logger=logger
            )

            # 解析样式中的位置和大小信息
            import re

            # 提取 background-position
            pos_match = re.search(r'background-position:\s*([^;]+)', style)
            bg_pos = pos_match.group(1) if pos_match else "0px 0px"

            # 提取 width 和 height
            width_match = re.search(r'width:\s*([^;]+)', style)
            height_match = re.search(r'height:\s*([^;]+)', style)

            logging_utils.logger_print(
                msg=f"parsed style - bg_pos: {bg_pos}, width: {width_match.group(1) if width_match else 'None'}, height: {height_match.group(1) if height_match else 'None'}",
                custom_logger=logger
            )

            # 如果没有尺寸信息，尝试不同的策略
            if not (width_match and height_match):
                logging_utils.logger_print(
                    msg="no size info found, trying alternative approach",
                    custom_logger=logger
                )

                # 尝试从图片中自动检测滑块
                return await self._auto_detect_slider(full_image_data)

            # 解析数值
            def parse_px(value):
                if 'px' in value:
                    return float(value.replace('px', '').strip())
                elif '%' in value:
                    # 如果是百分比，暂时返回0，需要更复杂的处理
                    return 0
                else:
                    try:
                        return float(value.strip())
                    except:
                        return 0

            width = parse_px(width_match.group(1))
            height = parse_px(height_match.group(1))

            # 解析 background-position
            pos_parts = bg_pos.split()
            if len(pos_parts) >= 2:
                x_pos = parse_px(pos_parts[0])
                y_pos = parse_px(pos_parts[1])

                # background-position 负值表示图片相对于容器的偏移
                # 我们需要裁剪的是图片中被显示的部分
                # 如果 background-position 是 -70px，意味着图片向左偏移70px
                # 所以我们需要从图片的第70px开始裁剪
                x_offset = abs(x_pos)  # 取绝对值作为裁剪起始点
                y_offset = abs(y_pos)
            else:
                x_offset = y_offset = 0

            logging_utils.logger_print(
                msg=f"crop parameters - x: {x_offset}, y: {y_offset}, width: {width}, height: {height}",
                custom_logger=logger
            )

            # 使用PIL裁剪图片
            image = Image.open(io.BytesIO(full_image_data))
            img_width, img_height = image.size

            logging_utils.logger_print(
                msg=f"original image size: {img_width}x{img_height}",
                custom_logger=logger
            )

            # 确保裁剪区域在图片范围内
            x_offset = max(0, min(x_offset, img_width - 1))
            y_offset = max(0, min(y_offset, img_height - 1))
            right = min(x_offset + width, img_width)
            bottom = min(y_offset + height, img_height)

            if right <= x_offset or bottom <= y_offset:
                logging_utils.logger_print(
                    msg="invalid crop area, returning original image",
                    custom_logger=logger
                )
                return full_image_data

            logging_utils.logger_print(
                msg=f"final crop area: ({x_offset}, {y_offset}, {right}, {bottom})",
                custom_logger=logger
            )

            cropped = image.crop((x_offset, y_offset, right, bottom))

            # 转换为字节
            buffer = io.BytesIO()
            cropped.save(buffer, format='PNG')
            return buffer.getvalue()
            
        except Exception as e:
            logging_utils.logger_print(
                msg="error cropping slider image",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return full_image_data  # 返回原图作为备选

    async def _auto_detect_slider(self, image_data: bytes) -> Optional[bytes]:
        """自动检测并提取滑块图片。"""
        try:
            from PIL import Image, ImageFilter
            import numpy as np

            image = Image.open(io.BytesIO(image_data))
            img_width, img_height = image.size

            logging_utils.logger_print(
                msg=f"auto detecting slider in image: {img_width}x{img_height}",
                custom_logger=logger
            )

            # 转换为灰度图进行边缘检测
            gray = image.convert('L')

            # 应用边缘检测
            edges = gray.filter(ImageFilter.FIND_EDGES)

            # 转换为numpy数组进行分析
            edge_array = np.array(edges)

            # 寻找可能的滑块区域（通常在图片的某个角落或特定位置）
            # 这里使用简单的策略：寻找边缘密度较高的区域

            # 将图片分成网格，寻找边缘最多的区域
            grid_size = 20
            max_edges = 0
            best_region = None

            for y in range(0, img_height - grid_size, grid_size // 2):
                for x in range(0, img_width - grid_size, grid_size // 2):
                    region = edge_array[y:y+grid_size, x:x+grid_size]
                    edge_count = np.sum(region > 50)  # 边缘阈值

                    if edge_count > max_edges:
                        max_edges = edge_count
                        best_region = (x, y, x + grid_size, y + grid_size)

            if best_region:
                logging_utils.logger_print(
                    msg=f"detected potential slider region: {best_region}",
                    custom_logger=logger
                )

                # 扩展区域以包含完整的滑块
                x1, y1, x2, y2 = best_region
                padding = 10
                x1 = max(0, x1 - padding)
                y1 = max(0, y1 - padding)
                x2 = min(img_width, x2 + padding)
                y2 = min(img_height, y2 + padding)

                cropped = image.crop((x1, y1, x2, y2))

                buffer = io.BytesIO()
                cropped.save(buffer, format='PNG')
                return buffer.getvalue()
            else:
                logging_utils.logger_print(
                    msg="no slider region detected, returning original",
                    custom_logger=logger
                )
                return image_data

        except Exception as e:
            logging_utils.logger_print(
                msg="error in auto slider detection",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return image_data
    
    async def solve_captcha(self, max_retries: int = 3) -> bool:
        """解决滑块验证码。
        
        Args:
            max_retries: 最大重试次数
            
        Returns:
            是否成功解决验证码
        """
        for attempt in range(max_retries):
            logging_utils.logger_print(
                msg=f"solving tencent captcha (attempt {attempt + 1}/{max_retries})",
                custom_logger=logger
            )
            
            try:
                # 等待验证码加载
                if not await self.wait_for_captcha():
                    continue
                
                # 获取验证码图片
                bg_data, slider_data = await self.get_captcha_images()
                if not bg_data:
                    logging_utils.logger_print(
                        msg="failed to get captcha images",
                        custom_logger=logger
                    )
                    continue

                # 保存图片到本地（在传递给dddocr前）
                try:
                    # 转换背景图为PNG格式
                    from PIL import Image
                    import io

                    bg_img = Image.open(io.BytesIO(bg_data))
                    bg_img.save(f"captcha_bg_attempt_{attempt + 1}.png", format='PNG')
                    logging_utils.logger_print(
                        msg=f"background image saved as PNG: captcha_bg_attempt_{attempt + 1}.png",
                        custom_logger=logger
                    )

                    if slider_data:
                        slider_img = Image.open(io.BytesIO(slider_data))
                        slider_img.save(f"captcha_slider_attempt_{attempt + 1}.png", format='PNG')
                        logging_utils.logger_print(
                            msg=f"slider image saved as PNG: captcha_slider_attempt_{attempt + 1}.png",
                            custom_logger=logger
                        )
                except Exception as save_error:
                    logging_utils.logger_print(
                        msg=f"failed to save images: {save_error}",
                        custom_logger=logger
                    )

                # 使用dddocr识别滑块位置
                if slider_data:
                    # 使用capcode API（推荐用于腾讯验证码）
                    target_x = self.dddocr_client.capcode(slider_data, bg_data)
                else:
                    # 如果没有滑块图，使用背景图进行识别
                    target_x = self.dddocr_client.capcode(bg_data, bg_data)
                
                logging_utils.logger_print(
                    msg=f"detected target position: x={target_x}",
                    custom_logger=logger
                )
                
                # 执行拖拽
                success = await self._drag_slider(target_x)
                if not success:
                    continue
                
                # 等待验证结果
                result = await self._wait_for_result()
                if result:
                    logging_utils.logger_print(
                        msg="tencent captcha solved successfully",
                        custom_logger=logger
                    )
                    return True
                
                # 如果失败，等待一段时间后重试
                await asyncio.sleep(2)
                
            except Exception as e:
                logging_utils.logger_print(
                    msg=f"error in attempt {attempt + 1}",
                    custom_logger=logger,
                    use_exception=True,
                    exception=e
                )
        
        logging_utils.logger_print(
            msg=f"failed to solve tencent captcha after {max_retries} attempts",
            custom_logger=logger
        )
        return False
    
    async def _drag_slider(self, target_x: int) -> bool:
        """拖拽滑块。"""
        try:
            logging_utils.logger_print(
                msg=f"starting drag operation for target_x: {target_x}",
                custom_logger=logger
            )

            # 确保没有焦点在输入框上，避免意外的键盘操作
            try:
                await self.page.evaluate("() => { if (document.activeElement) document.activeElement.blur(); }")
                logging_utils.logger_print(
                    msg="cleared focus from active element",
                    custom_logger=logger
                )
            except Exception as blur_error:
                logging_utils.logger_print(
                    msg=f"failed to clear focus: {blur_error}",
                    custom_logger=logger
                )

            # 获取滑块按钮
            slider_button = await self.page.query_selector('.tc-slider-normal')
            if not slider_button:
                logging_utils.logger_print(
                    msg="slider button not found",
                    custom_logger=logger
                )
                return False

            # 获取按钮位置
            button_box = await slider_button.bounding_box()
            if not button_box:
                logging_utils.logger_print(
                    msg="slider button bounding box not found",
                    custom_logger=logger
                )
                return False

            start_x = button_box['x'] + button_box['width'] / 2
            start_y = button_box['y'] + button_box['height'] / 2
            end_x = start_x + target_x

            logging_utils.logger_print(
                msg=f"slider button position: ({start_x}, {start_y}), target: {target_x}px",
                custom_logger=logger
            )
            
            # 执行拖拽 - 需要在 iframe 内进行拖拽
            # 但是使用主页面的坐标系统
            logging_utils.logger_print(
                msg="starting drag execution",
                custom_logger=logger
            )

            # 获取 iframe 在主页面中的位置
            iframe_element = await self.main_page.query_selector('#tcaptcha_iframe_dy')
            if iframe_element:
                iframe_box = await iframe_element.bounding_box()
                if iframe_box:
                    # 调整坐标到主页面坐标系
                    main_start_x = iframe_box['x'] + start_x
                    main_start_y = iframe_box['y'] + start_y
                    main_end_x = main_start_x + target_x

                    logging_utils.logger_print(
                        msg=f"iframe position: ({iframe_box['x']}, {iframe_box['y']})",
                        custom_logger=logger
                    )
                    logging_utils.logger_print(
                        msg=f"main page coordinates: start=({main_start_x}, {main_start_y}), end=({main_end_x}, {main_start_y})",
                        custom_logger=logger
                    )

                    # 使用主页面的 mouse 进行拖拽
                    logging_utils.logger_print(
                        msg="moving mouse to start position",
                        custom_logger=logger
                    )
                    await self.main_page.mouse.move(main_start_x, main_start_y)

                    logging_utils.logger_print(
                        msg="pressing mouse down",
                        custom_logger=logger
                    )
                    await self.main_page.mouse.down()

                    # 分步移动
                    steps = max(10, abs(target_x) // 5)
                    logging_utils.logger_print(
                        msg=f"dragging in {steps} steps",
                        custom_logger=logger
                    )

                    for i in range(1, steps + 1):
                        progress = i / steps
                        current_x = main_start_x + target_x * progress
                        await self.main_page.mouse.move(current_x, main_start_y)
                        await asyncio.sleep(0.02)

                    logging_utils.logger_print(
                        msg="releasing mouse",
                        custom_logger=logger
                    )
                    await self.main_page.mouse.up()

                    logging_utils.logger_print(
                        msg="drag operation completed",
                        custom_logger=logger
                    )
                else:
                    raise Exception("无法获取 iframe 边界框")
            else:
                raise Exception("无法找到 iframe 元素")

            logging_utils.logger_print(
                msg="waiting for drag result",
                custom_logger=logger
            )
            await self.page.wait_for_timeout(1000)
            
            return True
            
        except Exception as e:
            logging_utils.logger_print(
                msg="error dragging slider",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return False
    
    async def _wait_for_result(self, timeout: int = 10000) -> Optional[bool]:
        """等待验证结果。"""
        try:
            # 等待成功或失败状态
            result = await self.page.wait_for_selector(
                '.tc-success, .tc-error',
                timeout=timeout
            )
            
            if result:
                class_name = await result.get_attribute('class')
                if 'tc-success' in class_name:
                    return True
                elif 'tc-error' in class_name:
                    return False
            
            return None
            
        except Exception:
            return None

    async def drag_slider(self, target_x: int) -> bool:
        """公共方法：拖拽滑块到指定位置。

        Args:
            target_x: 目标X坐标

        Returns:
            是否拖拽成功
        """
        return await self._drag_slider(target_x)

    async def _screenshot_element(self, element) -> Optional[bytes]:
        """截图元素获取滑块图片。"""
        try:
            # 获取元素的边界框
            bounding_box = await element.bounding_box()
            if not bounding_box:
                logging_utils.logger_print(
                    msg="element has no bounding box",
                    custom_logger=logger
                )
                return None

            logging_utils.logger_print(
                msg=f"element bounding box: {bounding_box}",
                custom_logger=logger
            )

            # 直接截图元素
            screenshot_data = await element.screenshot()

            if screenshot_data:
                logging_utils.logger_print(
                    msg=f"element screenshot captured: {len(screenshot_data)} bytes",
                    custom_logger=logger
                )
                return screenshot_data
            else:
                logging_utils.logger_print(
                    msg="element screenshot failed",
                    custom_logger=logger
                )
                return None

        except Exception as e:
            logging_utils.logger_print(
                msg="error taking element screenshot",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return None
